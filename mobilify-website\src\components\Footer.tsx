import React from 'react';
import Logo from './Logo';
import NewsletterSignup from './NewsletterSignup';
import SimpleDarkModeToggle from './SimpleDarkModeToggle';
import NoSSR from './NoSSR';
import { Card, CardContent } from './ui';

const Footer = () => {
  return (
    <footer className="bg-dark-charcoal dark:bg-gray-950 text-white py-12 transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-4">
              <Logo />
              <span className="ml-2 text-xl font-bold text-white">Mobilify</span>
            </div>
            <p className="text-gray-400 dark:text-gray-300 text-sm leading-relaxed">
              Transforming ideas into mobile reality. We help entrepreneurs and businesses
              create beautiful, high-performance mobile apps without the traditional complexity and cost.
            </p>
          </div>

          {/* Quick Links */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4 text-white">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="/#demo" className="text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200">
                  See Demo
                </a>
              </li>
              <li>
                <a href="/services" className="text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200">
                  Services & Pricing
                </a>
              </li>
              <li>
                <a href="/about" className="text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200">
                  About Us
                </a>
              </li>
              <li>
                <a href="/#contact" className="text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200">
                  Contact
                </a>
              </li>
            </ul>
          </div>

          {/* Newsletter Signup */}
          <div className="lg:col-span-1">
            <NewsletterSignup variant="footer" />
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 dark:border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <p className="text-gray-400 dark:text-gray-300 text-sm">
              © 2024 Mobilify. All rights reserved.
            </p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <NoSSR>
                <SimpleDarkModeToggle size="sm" />
              </NoSSR>
              <a href="#" className="text-gray-400 dark:text-gray-300 hover:text-electric-blue text-sm transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 dark:text-gray-300 hover:text-electric-blue text-sm transition-colors duration-200">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
