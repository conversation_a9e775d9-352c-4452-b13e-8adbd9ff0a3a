'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, CheckCircle, AlertCircle } from 'lucide-react';
import { Button, Input } from './ui';

interface NewsletterSignupProps {
  variant?: 'footer' | 'section';
  className?: string;
}

const NewsletterSignup: React.FC<NewsletterSignupProps> = ({ 
  variant = 'footer', 
  className = '' 
}) => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setStatus('error');
      setMessage('Please enter a valid email address');
      return;
    }

    setStatus('loading');
    
    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        setMessage('Thanks for subscribing! Check your email for confirmation.');
        setEmail('');
        
        // Track newsletter signup event
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'newsletter_signup', {
            event_category: 'engagement',
            event_label: variant
          });
        }
      } else {
        setStatus('error');
        setMessage(data.error || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Network error. Please check your connection and try again.');
    }

    // Reset status after 5 seconds
    setTimeout(() => {
      setStatus('idle');
      setMessage('');
    }, 5000);
  };

  if (variant === 'footer') {
    return (
      <div className={`space-y-4 ${className}`}>
        <h3 className="text-lg font-semibold text-dark-charcoal dark:text-white">
          Stay Updated
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Get the latest mobile app development tips and Mobilify updates.
        </p>
        
        <form onSubmit={handleSubmit} className="space-y-3">
          <div className="flex flex-col sm:flex-row gap-2">
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className="flex-1 text-sm"
              disabled={status === 'loading'}
            />
            <Button
              type="submit"
              disabled={status === 'loading' || !email.trim()}
              variant="primary"
              size="sm"
              className="whitespace-nowrap"
              isLoading={status === 'loading'}
            >
              {status === 'loading' ? 'Subscribing...' : 'Subscribe'}
            </Button>
          </div>
          
          {message && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`flex items-center gap-2 text-sm ${
                status === 'success' ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {status === 'success' ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                <AlertCircle className="w-4 h-4" />
              )}
              {message}
            </motion.div>
          )}
        </form>
      </div>
    );
  }

  // Section variant
  return (
    <section className={`py-16 bg-gradient-to-r from-electric-blue to-indigo-600 ${className}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="space-y-6"
        >
          <div className="flex justify-center">
            <div className="bg-white/20 rounded-full p-3">
              <Mail className="w-8 h-8 text-white" />
            </div>
          </div>
          
          <h2 className="text-3xl sm:text-4xl font-bold text-white">
            Get Mobile App Insights
          </h2>
          
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            Join 500+ entrepreneurs getting weekly tips on mobile app development, 
            industry trends, and exclusive Mobilify updates.
          </p>
          
          <form onSubmit={handleSubmit} className="max-w-md mx-auto">
            <div className="flex flex-col sm:flex-row gap-3">
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="flex-1 border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-electric-blue text-dark-charcoal bg-white"
                disabled={status === 'loading'}
              />
              <Button
                type="submit"
                disabled={status === 'loading' || !email.trim()}
                variant="secondary"
                size="md"
                className="bg-white text-electric-blue hover:bg-gray-50 whitespace-nowrap"
                isLoading={status === 'loading'}
              >
                {status === 'loading' ? 'Subscribing...' : 'Get Updates'}
              </button>
            </div>
            
            {message && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`mt-4 flex items-center justify-center gap-2 ${
                  status === 'success' ? 'text-green-100' : 'text-red-100'
                }`}
              >
                {status === 'success' ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <AlertCircle className="w-5 h-5" />
                )}
                {message}
              </motion.div>
            )}
          </form>
          
          <p className="text-blue-200 text-sm">
            No spam, ever. Unsubscribe with one click.
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default NewsletterSignup;
