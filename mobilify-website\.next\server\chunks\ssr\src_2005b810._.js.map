{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Logo = ({ className = \"\" }: { className?: string }) => {\n  return (\n    <div className={`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${className}`}>\n      <span className=\"text-white font-bold text-xl\">M</span>\n    </div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAA0B;IACtD,qBACE,8OAAC;QAAI,WAAW,CAAC,yEAAyE,EAAE,WAAW;kBACrG,cAAA,8OAAC;YAAK,WAAU;sBAA+B;;;;;;;;;;;AAGrD;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleHeaderChat.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle } from 'lucide-react';\n\nconst SimpleHeaderChat: React.FC = () => {\n  const handleChatOpen = () => {\n    // Try to open Crisp chat if available\n    if (typeof window !== 'undefined' && (window as any).$crisp) {\n      (window as any).$crisp.push(['do', 'chat:open']);\n    } else {\n      // Fallback to mailto if Crisp is not available\n      window.location.href = 'mailto:<EMAIL>?subject=Chat%20Request';\n    }\n\n    // Track chat interaction for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_opened', {\n        event_category: 'engagement',\n        event_label: 'header_chat'\n      });\n    }\n  };\n\n  return (\n    <button\n      onClick={handleChatOpen}\n      className=\"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200\"\n      aria-label=\"Open chat\"\n    >\n      <MessageCircle className=\"w-5 h-5\" />\n      <span className=\"hidden sm:inline\">Chat</span>\n    </button>\n  );\n};\n\nexport default SimpleHeaderChat;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,mBAA6B;IACjC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,uCAA6D;;QAE7D,OAAO;YACL,+CAA+C;YAC/C,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,uCAAuC;QACvC,uCAA2D;;QAK3D;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;;0BAEX,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC;gBAAK,WAAU;0BAAmB;;;;;;;;;;;;AAGzC;uCAEe", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleDarkModeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Sun, Moon } from 'lucide-react';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface SimpleDarkModeToggleProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst SimpleDarkModeToggle: React.FC<SimpleDarkModeToggleProps> = ({\n  className = '',\n  size = 'md'\n}) => {\n  const { theme, toggleTheme } = useTheme();\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'w-8 h-8 text-sm';\n      case 'lg':\n        return 'w-12 h-12 text-lg';\n      default:\n        return 'w-10 h-10 text-base';\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className={`${getSizeClasses()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${className}`}\n      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}\n    >\n      {theme === 'dark' ? (\n        <Sun className=\"w-5 h-5\" />\n      ) : (\n        <Moon className=\"w-5 h-5\" />\n      )}\n    </button>\n  );\n};\n\nexport default SimpleDarkModeToggle;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAJA;;;;AAWA,MAAM,uBAA4D,CAAC,EACjE,YAAY,EAAE,EACd,OAAO,IAAI,EACZ;IACC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEtC,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,GAAG,iBAAiB,gLAAgL,EAAE,WAAW;QAC5N,cAAY,CAAC,UAAU,EAAE,UAAU,SAAS,UAAU,OAAO,KAAK,CAAC;kBAElE,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;iCAEf,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\nimport Logo from './Logo';\nimport NoSSR from './NoSSR';\nimport SimpleHeaderChat from './SimpleHeaderChat';\nimport SimpleDarkModeToggle from './SimpleDarkModeToggle';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMenuOpen(false);\n  };\n\n  const navItems = [\n    { label: 'Services', href: '#services-overview' },\n    { label: 'How It Works', href: '#process' },\n    { label: 'About Us', href: '#about' },\n  ];\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300\">\n      <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 w-full\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n            <span className=\"ml-2 text-xl font-bold text-dark-charcoal dark:text-white\">Mobilify</span>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <button\n                key={item.label}\n                onClick={() => scrollToSection(item.href.substring(1))}\n                className=\"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200\"\n              >\n                {item.label}\n              </button>\n            ))}\n            <NoSSR>\n              <SimpleHeaderChat />\n            </NoSSR>\n            <NoSSR>\n              <SimpleDarkModeToggle size=\"sm\" className=\"mr-4\" />\n            </NoSSR>\n            <button\n              onClick={() => scrollToSection('contact')}\n              className=\"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200\"\n            >\n              Get a Quote\n            </button>\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100\"\n          >\n            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <NoSSR>\n        {isMenuOpen && (\n          <div className=\"md:hidden fixed inset-0 top-16 bg-white z-40\">\n            <div className=\"px-4 py-6 space-y-4\">\n              {navItems.map((item) => (\n                <button\n                  key={item.label}\n                  onClick={() => scrollToSection(item.href.substring(1))}\n                  className=\"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2\"\n                >\n                  {item.label}\n                </button>\n              ))}\n              <button\n                onClick={() => scrollToSection('contact')}\n                className=\"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6\"\n              >\n                Get a Quote\n              </button>\n            </div>\n          </div>\n        )}\n      </NoSSR>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,cAAc;IAChB;IAEA,MAAM,WAAW;QACf;YAAE,OAAO;YAAY,MAAM;QAAqB;QAChD;YAAE,OAAO;YAAgB,MAAM;QAAW;QAC1C;YAAE,OAAO;YAAY,MAAM;QAAS;KACrC;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,UAAI;;;;;8CACL,8OAAC;oCAAK,WAAU;8CAA4D;;;;;;;;;;;;sCAI9E,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;wCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI,CAAC,SAAS,CAAC;wCACnD,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,KAAK;;;;;8CAOnB,8OAAC,2HAAA,CAAA,UAAK;8CACJ,cAAA,8OAAC,sIAAA,CAAA,UAAgB;;;;;;;;;;8CAEnB,8OAAC,2HAAA,CAAA,UAAK;8CACJ,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAK,WAAU;;;;;;;;;;;8CAE5C,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMlD,8OAAC,2HAAA,CAAA,UAAK;0BACH,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI,CAAC,SAAS,CAAC;oCACnD,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,KAAK;;;;;0CAOnB,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading = false, disabled, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'bg-electric-blue text-white hover:opacity-90 focus:ring-electric-blue shadow-lg hover:shadow-xl',\n      secondary: 'border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white focus:ring-electric-blue',\n      ghost: 'text-electric-blue hover:bg-electric-blue hover:bg-opacity-10 focus:ring-electric-blue'\n    };\n\n    const sizes = {\n      sm: 'px-4 py-2 text-sm',\n      md: 'px-6 py-3 text-base',\n      lg: 'px-8 py-4 text-lg'\n    };\n\n    const buttonClasses = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    return (\n      <motion.button\n        ref={ref}\n        className={buttonClasses}\n        disabled={disabled || isLoading}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        transition={{ duration: 0.2 }}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-3 h-5 w-5\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </motion.button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,YAAY;YAAE,UAAU;QAAI;QAC3B,GAAG,KAAK;kBAER,0BACC;;8BACE,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;;sCAER,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAEA;;2BAIR;;;;;;AAIR;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  variant?: 'base' | 'error' | 'success';\n  label?: string;\n  helperText?: string;\n  errorMessage?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, variant = 'base', label, helperText, errorMessage, ...props }, ref) => {\n    const baseClasses = 'w-full px-4 py-3 rounded-lg text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      base: 'border border-gray-300 dark:border-gray-600 focus:ring-electric-blue focus:border-electric-blue bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400',\n      error: 'border border-red-500 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400',\n      success: 'border border-green-500 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400'\n    };\n\n    const inputClasses = cn(\n      baseClasses,\n      variants[variant],\n      className\n    );\n\n    const displayErrorMessage = variant === 'error' && errorMessage;\n    const displayHelperText = variant !== 'error' && helperText;\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {label}\n          </label>\n        )}\n        <input\n          ref={ref}\n          className={inputClasses}\n          {...props}\n        />\n        {displayErrorMessage && (\n          <p className=\"mt-2 text-sm text-red-600 dark:text-red-400\">\n            {errorMessage}\n          </p>\n        )}\n        {displayHelperText && (\n          <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n            {helperText}\n          </p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,sBAAQ,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,EAAE;IAC3E,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACpB,aACA,QAAQ,CAAC,QAAQ,EACjB;IAGF,MAAM,sBAAsB,YAAY,WAAW;IACnD,MAAM,oBAAoB,YAAY,WAAW;IAEjD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACV,GAAG,KAAK;;;;;;YAEV,qCACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAGJ,mCACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'base' | 'hover' | 'interactive';\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'base', children, ...props }, ref) => {\n    const baseClasses = 'bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700';\n    \n    const variants = {\n      base: '',\n      hover: 'hover:shadow-md transition-shadow duration-200',\n      interactive: 'hover:shadow-lg cursor-pointer transition-shadow duration-200'\n    };\n\n    const cardClasses = cn(\n      baseClasses,\n      variants[variant],\n      className\n    );\n\n    return (\n      <div\n        ref={ref}\n        className={cardClasses}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\n// Card sub-components for better composition\nexport const CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6 pb-0', className)}\n      {...props}\n    />\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nexport const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6', className)}\n      {...props}\n    />\n  )\n);\nCardContent.displayName = 'CardContent';\n\nexport const CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6 pt-0', className)}\n      {...props}\n    />\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,MAAM;QACN,OAAO;QACP,aAAa;IACf;IAEA,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,aACA,QAAQ,CAAC,QAAQ,EACjB;IAGF,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAGZ,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAElB,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CACzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACpB,GAAG,KAAK;;;;;;AAIf,YAAY,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ui/index.ts"], "sourcesContent": ["export { default as But<PERSON> } from './Button';\nexport type { ButtonProps } from './Button';\n\nexport { default as Input } from './Input';\nexport type { InputProps } from './Input';\n\nexport { default as Card, CardHeader, CardContent, CardFooter } from './Card';\nexport type { CardProps } from './Card';\n"], "names": [], "mappings": ";AAAA;AAGA;AAGA", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui';\n\nconst Hero = () => {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"hero\" className=\"pt-16 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 min-h-screen flex items-center w-full transition-colors duration-300\">\n      <div className=\"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center w-full\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.7 }}\n            className=\"text-center lg:text-left\"\n          >\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-dark-charcoal dark:text-white leading-tight\">\n              Your Idea. Your App.{' '}\n              <span className=\"text-electric-blue\">Realized.</span>\n            </h1>\n\n            <p className=\"mt-6 text-xl text-gray-600 dark:text-gray-300 leading-relaxed\">\n              Mobilify transforms your concepts and existing websites into stunning, \n              high-performance mobile apps. We are the bridge from vision to launch.\n            </p>\n            \n            <div className=\"mt-8\">\n              <Button\n                onClick={() => scrollToSection('demo')}\n                variant=\"primary\"\n                size=\"lg\"\n              >\n                See How It Works\n              </Button>\n            </div>\n          </motion.div>\n\n          {/* Visual */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.7, delay: 0.2 }}\n            className=\"relative\"\n          >\n            <div className=\"relative mx-auto w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl\">\n              {/* Phone frame */}\n              <div className=\"w-full h-full bg-white rounded-2xl overflow-hidden relative\">\n                {/* Status bar */}\n                <div className=\"h-6 bg-gray-900 flex items-center justify-center\">\n                  <div className=\"w-16 h-1 bg-white rounded-full\"></div>\n                </div>\n                \n                {/* App content placeholder */}\n                <div className=\"p-4 space-y-4\">\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: 1 }}\n                    className=\"h-4 bg-gray-200 rounded animate-pulse\"\n                  ></motion.div>\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: 1.2 }}\n                    className=\"h-32 bg-gradient-to-r from-electric-blue/10 to-electric-blue/20 rounded-lg flex items-center justify-center\"\n                  >\n                    <div className=\"text-electric-blue font-semibold\">Your App Here</div>\n                  </motion.div>\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: 1.4 }}\n                    className=\"space-y-2\"\n                  >\n                    <div className=\"h-3 bg-gray-200 rounded\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n                  </motion.div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAMA,MAAM,OAAO;IACX,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAA8F;oCACrF;kDACrB,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAE,WAAU;0CAAgE;;;;;;0CAK7E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uKAAA,CAAA,SAAM;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,SAAQ;oCACR,MAAK;8CACN;;;;;;;;;;;;;;;;;kCAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAE;gDACtC,WAAU;;;;;;0DAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;0DAEpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnC;uCAEe", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/InteractiveDemo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst InteractiveDemo = () => {\n  const [activeTab, setActiveTab] = useState('website');\n  const [inputValue, setInputValue] = useState('');\n  const [showDemo, setShowDemo] = useState(false);\n\n  const handlePreview = () => {\n    if (inputValue.trim()) {\n      setShowDemo(true);\n      // Track demo interaction event (placeholder for GA4)\n      if (typeof window !== 'undefined' && (window as any).gtag) {\n        (window as any).gtag('event', 'demo_interaction', {\n          event_category: 'engagement',\n          event_label: activeTab\n        });\n      }\n    }\n  };\n\n  const handleTabSwitch = (tab: string) => {\n    setActiveTab(tab);\n    resetDemo();\n    // Track tab switch event (placeholder for GA4)\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'tab_switch', {\n        event_category: 'engagement',\n        event_label: tab\n      });\n    }\n  };\n\n  const handleAnimationComplete = () => {\n    // Track demo animation complete event (placeholder for GA4)\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'demo_animation_complete', {\n        event_category: 'engagement',\n        event_label: activeTab\n      });\n    }\n  };\n\n  const resetDemo = () => {\n    setShowDemo(false);\n    setInputValue('');\n  };\n\n  return (\n    <section id=\"demo\" className=\"py-20 bg-white dark:bg-gray-900 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-dark-charcoal mb-4\">\n            From Zero to App, Instantly.\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            See how quickly we can transform your vision into a beautiful mobile app\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Input Section */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            {/* Tabs */}\n            <div className=\"flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1\">\n              <button\n                onClick={() => handleTabSwitch('website')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${\n                  activeTab === 'website'\n                    ? 'bg-white dark:bg-gray-700 text-dark-charcoal dark:text-white shadow-sm'\n                    : 'text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white'\n                }`}\n              >\n                Convert a Website\n              </button>\n              <button\n                onClick={() => handleTabSwitch('idea')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${\n                  activeTab === 'idea'\n                    ? 'bg-white dark:bg-gray-700 text-dark-charcoal dark:text-white shadow-sm'\n                    : 'text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white'\n                }`}\n              >\n                Describe an Idea\n              </button>\n            </div>\n\n            {/* Input Field */}\n            <div className=\"space-y-4\">\n              <input\n                type=\"text\"\n                value={inputValue}\n                onChange={(e) => setInputValue(e.target.value)}\n                placeholder={\n                  activeTab === 'website'\n                    ? 'Enter your website URL (e.g., https://example.com)'\n                    : 'Describe your app idea (e.g., A fitness tracking app for runners)'\n                }\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-transparent\"\n              />\n\n              <button\n                onClick={handlePreview}\n                disabled={!inputValue.trim()}\n                className=\"w-full bg-electric-blue text-white py-3 px-6 rounded-lg font-semibold hover:opacity-90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200\"\n              >\n                Mobilify Preview\n              </button>\n            </div>\n          </motion.div>\n\n          {/* Phone Mockup */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"flex justify-center\"\n          >\n            <div className=\"relative\">\n              <div className=\"w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl\">\n                <div className=\"w-full h-full bg-gray-800 rounded-2xl overflow-hidden relative\">\n                  {/* Status bar */}\n                  <div className=\"h-6 bg-gray-900 flex items-center justify-center\">\n                    <div className=\"w-16 h-1 bg-white rounded-full\"></div>\n                  </div>\n                  \n                  {/* App content */}\n                  <div className=\"p-4 h-full bg-gray-800 text-white\">\n                    <AnimatePresence mode=\"wait\">\n                      {!showDemo ? (\n                        <motion.div\n                          key=\"placeholder\"\n                          initial={{ opacity: 1 }}\n                          exit={{ opacity: 0 }}\n                          className=\"flex items-center justify-center h-full\"\n                        >\n                          <div className=\"text-center text-gray-400\">\n                            <div className=\"w-16 h-16 bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center\">\n                              <span className=\"text-2xl\">📱</span>\n                            </div>\n                            <p className=\"text-sm\">Your app preview will appear here</p>\n                          </div>\n                        </motion.div>\n                      ) : (\n                        <motion.div\n                          key=\"demo\"\n                          initial={{ opacity: 0, y: 20 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ duration: 0.5 }}\n                          onAnimationComplete={handleAnimationComplete}\n                          className=\"space-y-4\"\n                        >\n                          {/* Header */}\n                          <motion.div\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ duration: 0.5, delay: 0.2 }}\n                            className=\"text-lg font-semibold\"\n                          >\n                            Hello, Alex\n                          </motion.div>\n                          \n                          {/* Chart */}\n                          <motion.div\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ duration: 0.5, delay: 0.4 }}\n                            className=\"bg-gray-700 rounded-lg p-4 h-32 flex items-end justify-between\"\n                          >\n                            {[40, 65, 45, 80, 60, 90, 75].map((height, index) => (\n                              <motion.div\n                                key={index}\n                                initial={{ height: 0 }}\n                                animate={{ height: `${height}%` }}\n                                transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}\n                                className=\"bg-electric-blue w-4 rounded-t\"\n                              />\n                            ))}\n                          </motion.div>\n                          \n                          {/* Active Projects */}\n                          <motion.div\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ duration: 0.5, delay: 1.2 }}\n                            className=\"space-y-2\"\n                          >\n                            <h3 className=\"font-medium text-sm\">Active Projects</h3>\n                            <div className=\"space-y-2\">\n                              <div className=\"flex items-center justify-between bg-gray-700 rounded p-2\">\n                                <span className=\"text-sm\">Mobile App Design</span>\n                                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                              </div>\n                              <div className=\"flex items-center justify-between bg-gray-700 rounded p-2\">\n                                <span className=\"text-sm\">Website Redesign</span>\n                                <div className=\"w-2 h-2 bg-yellow-500 rounded-full\"></div>\n                              </div>\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </div>\n                  \n                  {/* Bottom Navigation */}\n                  {showDemo && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ duration: 0.5, delay: 1.5 }}\n                      className=\"absolute bottom-2 left-2 right-2 bg-gray-700 rounded-lg p-2 flex justify-around\"\n                    >\n                      <div className=\"w-6 h-6 bg-electric-blue rounded flex items-center justify-center\">\n                        <span className=\"text-xs\">🏠</span>\n                      </div>\n                      <div className=\"w-6 h-6 bg-gray-600 rounded flex items-center justify-center\">\n                        <span className=\"text-xs\">📊</span>\n                      </div>\n                      <div className=\"w-6 h-6 bg-gray-600 rounded flex items-center justify-center\">\n                        <span className=\"text-xs\">⚙️</span>\n                      </div>\n                    </motion.div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default InteractiveDemo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,kBAAkB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,gBAAgB;QACpB,IAAI,WAAW,IAAI,IAAI;YACrB,YAAY;YACZ,qDAAqD;YACrD,uCAA2D;;YAK3D;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb;QACA,+CAA+C;QAC/C,uCAA2D;;QAK3D;IACF;IAEA,MAAM,0BAA0B;QAC9B,4DAA4D;QAC5D,uCAA2D;;QAK3D;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;QACZ,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAK5E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,+EAA+E,EACzF,cAAc,YACV,2EACA,mFACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,+EAA+E,EACzF,cAAc,SACV,2EACA,mFACJ;sDACH;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aACE,cAAc,YACV,uDACA;4CAEN,WAAU;;;;;;sDAGZ,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC,WAAW,IAAI;4CAC1B,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;0DAIjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oDAAC,MAAK;8DACnB,CAAC,yBACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,SAAS;wDAAE;wDACtB,MAAM;4DAAE,SAAS;wDAAE;wDACnB,WAAU;kEAEV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAW;;;;;;;;;;;8EAE7B,8OAAC;oEAAE,WAAU;8EAAU;;;;;;;;;;;;uDATrB;;;;6EAaN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,YAAY;4DAAE,UAAU;wDAAI;wDAC5B,qBAAqB;wDACrB,WAAU;;0EAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;gEACxC,WAAU;0EACX;;;;;;0EAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;gEACxC,WAAU;0EAET;oEAAC;oEAAI;oEAAI;oEAAI;oEAAI;oEAAI;oEAAI;iEAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEAET,SAAS;4EAAE,QAAQ;wEAAE;wEACrB,SAAS;4EAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;wEAAC;wEAChC,YAAY;4EAAE,UAAU;4EAAK,OAAO,MAAM,QAAQ;wEAAI;wEACtD,WAAU;uEAJL;;;;;;;;;;0EAUX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;gEACxC,WAAU;;kFAEV,8OAAC;wEAAG,WAAU;kFAAsB;;;;;;kFACpC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAU;;;;;;kGAC1B,8OAAC;wFAAI,WAAU;;;;;;;;;;;;0FAEjB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAU;;;;;;kGAC1B,8OAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;uDAlDjB;;;;;;;;;;;;;;;4CA4DX,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlD;uCAEe", "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ServicesOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowRight, Smartphone, Lightbulb, Building } from 'lucide-react';\nimport Link from 'next/link';\n\nconst ServicesOverview = () => {\n  const services = [\n    {\n      icon: <Smartphone className=\"w-8 h-8\" />,\n      title: 'Starter App',\n      description: 'Perfect for converting existing websites into mobile apps.',\n      price: 'Starting at $5,000',\n      features: ['Website Conversion', 'iOS & Android', 'Basic Features'],\n    },\n    {\n      icon: <Lightbulb className=\"w-8 h-8\" />,\n      title: 'Custom App',\n      description: 'Turn your new ideas into reality with custom development.',\n      price: 'Starting at $15,000',\n      features: ['Idea to App', 'Custom UI/UX', 'Advanced Features'],\n      popular: true,\n    },\n    {\n      icon: <Building className=\"w-8 h-8\" />,\n      title: 'Enterprise',\n      description: 'Complex projects needing deep integration and support.',\n      price: 'Custom Pricing',\n      features: ['Bespoke Solutions', 'Full Integration', '24/7 Support'],\n    },\n  ];\n\n  const handleViewServices = () => {\n    // Track view services details event (placeholder for GA4)\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'view_services_details', {\n        event_category: 'engagement',\n        event_label: 'services_overview'\n      });\n    }\n  };\n\n  return (\n    <section id=\"services-overview\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-dark-charcoal mb-4\">\n            Solutions Tailored for Your Needs\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Whether you're converting a website or building from scratch, we have a package for you.\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n          {services.map((service, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className={`relative bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 ${\n                service.popular ? 'ring-2 ring-electric-blue' : ''\n              }`}\n            >\n              {service.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <span className=\"bg-electric-blue text-white px-4 py-1 rounded-full text-sm font-medium\">\n                    Most Popular\n                  </span>\n                </div>\n              )}\n              \n              <div className=\"text-electric-blue mb-4\">\n                {service.icon}\n              </div>\n              \n              <h3 className=\"text-xl font-bold text-dark-charcoal mb-2\">\n                {service.title}\n              </h3>\n\n              <p className=\"text-gray-600 mb-4\">\n                {service.description}\n              </p>\n\n              <div className=\"text-2xl font-bold text-dark-charcoal mb-4\">\n                {service.price}\n              </div>\n              \n              <ul className=\"space-y-2 mb-6\">\n                {service.features.map((feature, featureIndex) => (\n                  <li key={featureIndex} className=\"flex items-center text-gray-600\">\n                    <div className=\"w-2 h-2 bg-electric-blue rounded-full mr-3\"></div>\n                    {feature}\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <Link\n            href=\"/services\"\n            onClick={handleViewServices}\n            className=\"inline-flex items-center bg-electric-blue text-white px-8 py-4 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl\"\n          >\n            Compare All Features & Pricing\n            <ArrowRight className=\"ml-2 w-5 h-5\" />\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesOverview;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AALA;;;;;AAOA,MAAM,mBAAmB;IACvB,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;gBAAC;gBAAsB;gBAAiB;aAAiB;QACrE;QACA;YACE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;gBAAC;gBAAe;gBAAgB;aAAoB;YAC9D,SAAS;QACX;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,OAAO;YACP,UAAU;gBAAC;gBAAqB;gBAAoB;aAAe;QACrE;KACD;IAED,MAAM,qBAAqB;QACzB,0DAA0D;QAC1D,uCAA2D;;QAK3D;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAoB,WAAU;kBACxC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAW,CAAC,0FAA0F,EACpG,QAAQ,OAAO,GAAG,8BAA8B,IAChD;;gCAED,QAAQ,OAAO,kBACd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAyE;;;;;;;;;;;8CAM7F,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAGf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAGhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,KAAK;;;;;;8CAGhB,8OAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;4CAAsB,WAAU;;8DAC/B,8OAAC;oDAAI,WAAU;;;;;;gDACd;;2CAFM;;;;;;;;;;;2BAnCR;;;;;;;;;;8BA6CX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,SAAS;wBACT,WAAU;;4BACX;0CAEC,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Process.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Search, Palette, Rocket } from 'lucide-react';\nimport { Card, CardContent } from './ui';\n\nconst Process = () => {\n  const steps = [\n    {\n      number: '01',\n      icon: <Search className=\"w-8 h-8\" />,\n      title: 'Discovery & Strategy',\n      description: 'We dive deep into your vision and goals, understanding your target audience and business objectives to create the perfect roadmap.',\n    },\n    {\n      number: '02',\n      icon: <Palette className=\"w-8 h-8\" />,\n      title: 'Design & Development',\n      description: 'Our team builds your app with precision and care, focusing on user experience, performance, and beautiful design that represents your brand.',\n    },\n    {\n      number: '03',\n      icon: <Rocket className=\"w-8 h-8\" />,\n      title: 'Launch & Support',\n      description: 'We handle app store submission and provide ongoing support to ensure your app succeeds in the market and continues to evolve.',\n    },\n  ];\n\n  return (\n    <section id=\"process\" className=\"py-20 bg-white dark:bg-gray-900 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-dark-charcoal dark:text-white mb-4\">\n            Your Clear Path to Launch\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            Our proven process ensures your app is built right, launched successfully, and supported long-term\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-3 gap-8 lg:gap-12\">\n          {steps.map((step, index) => (\n            <motion.div\n              key={step.number}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              className=\"relative text-center\"\n            >\n              {/* Connection line for desktop */}\n              {index < steps.length - 1 && (\n                <div className=\"hidden md:block absolute top-16 left-1/2 w-full h-0.5 bg-gray-200 dark:bg-gray-700 z-0\">\n                  <motion.div\n                    initial={{ width: 0 }}\n                    whileInView={{ width: '100%' }}\n                    transition={{ duration: 1, delay: index * 0.3 + 0.5 }}\n                    viewport={{ once: true }}\n                    className=\"h-full bg-electric-blue\"\n                  />\n                </div>\n              )}\n              \n              {/* Step number and icon */}\n              <div className=\"relative z-10 mb-6\">\n                <div className=\"inline-flex items-center justify-center w-16 h-16 bg-electric-blue text-white rounded-full mb-4 relative\">\n                  {step.icon}\n                  <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-white dark:bg-gray-800 text-electric-blue rounded-full flex items-center justify-center text-sm font-bold shadow-lg\">\n                    {step.number}\n                  </div>\n                </div>\n              </div>\n              \n              {/* Content */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-xl font-bold text-dark-charcoal dark:text-white\">\n                  {step.title}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300 leading-relaxed\">\n                  {step.description}\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Additional info */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardContent className=\"p-8 text-center\">\n              <h3 className=\"text-lg font-semibold text-dark-charcoal dark:text-white mb-2\">\n                Transparent Timeline\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                Most projects are completed within 4-8 weeks, depending on complexity.\n                We provide regular updates and maintain open communication throughout the entire process.\n              </p>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Process;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,UAAU;IACd,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAyE;;;;;;sCAGvF,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAK5E,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCAGT,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;wCAAE;wCACpB,aAAa;4CAAE,OAAO;wCAAO;wCAC7B,YAAY;4CAAE,UAAU;4CAAG,OAAO,QAAQ,MAAM;wCAAI;wCACpD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI;0DACV,8OAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM;;;;;;;;;;;;;;;;;8CAMlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;2BApChB,KAAK,MAAM;;;;;;;;;;8BA4CtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,mKAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D;uCAEe", "debugId": null}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/AboutSnippet.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ArrowRight } from 'lucide-react';\nimport { Card, CardContent } from './ui';\n\nconst AboutSnippet = () => {\n  return (\n    <section id=\"about\" className=\"py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"max-w-4xl mx-auto\"\n        >\n          <Card variant=\"hover\" className=\"text-center\">\n            <CardContent className=\"p-8 md:p-12\">\n              <h2 className=\"text-3xl sm:text-4xl font-bold text-dark-charcoal dark:text-white mb-6\">\n                We're More Than Just Developers\n              </h2>\n\n              <div className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 space-y-4\">\n                <p>\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We're passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                </p>\n\n                <p>\n                  Our commitment goes beyond just writing code – we're your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                </p>\n              </div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n                viewport={{ once: true }}\n              >\n                <Link\n                  href=\"/about\"\n                  className=\"inline-flex items-center text-electric-blue hover:text-electric-blue/80 font-semibold transition-colors duration-200\"\n                >\n                  Meet the Team\n                  <ArrowRight className=\"ml-2 w-4 h-4\" />\n                </Link>\n              </motion.div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutSnippet;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;AAQA,MAAM,eAAe;IACnB,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAEV,cAAA,8OAAC,mKAAA,CAAA,OAAI;oBAAC,SAAQ;oBAAQ,WAAU;8BAC9B,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAG,WAAU;0CAAyE;;;;;;0CAIvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDAMH,8OAAC;kDAAE;;;;;;;;;;;;0CAQL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;uCAEe", "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/NewsletterSignup.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Mail, CheckCircle, AlertCircle } from 'lucide-react';\nimport { Button, Input } from './ui';\n\ninterface NewsletterSignupProps {\n  variant?: 'footer' | 'section';\n  className?: string;\n}\n\nconst NewsletterSignup: React.FC<NewsletterSignupProps> = ({ \n  variant = 'footer', \n  className = '' \n}) => {\n  const [email, setEmail] = useState('');\n  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  const [message, setMessage] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!email.trim()) {\n      setStatus('error');\n      setMessage('Please enter a valid email address');\n      return;\n    }\n\n    setStatus('loading');\n    \n    try {\n      const response = await fetch('/api/newsletter', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setStatus('success');\n        setMessage('Thanks for subscribing! Check your email for confirmation.');\n        setEmail('');\n        \n        // Track newsletter signup event\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'newsletter_signup', {\n            event_category: 'engagement',\n            event_label: variant\n          });\n        }\n      } else {\n        setStatus('error');\n        setMessage(data.error || 'Something went wrong. Please try again.');\n      }\n    } catch (error) {\n      setStatus('error');\n      setMessage('Network error. Please check your connection and try again.');\n    }\n\n    // Reset status after 5 seconds\n    setTimeout(() => {\n      setStatus('idle');\n      setMessage('');\n    }, 5000);\n  };\n\n  if (variant === 'footer') {\n    return (\n      <div className={`space-y-4 ${className}`}>\n        <h3 className=\"text-lg font-semibold text-dark-charcoal dark:text-white\">\n          Stay Updated\n        </h3>\n        <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\n          Get the latest mobile app development tips and Mobilify updates.\n        </p>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-3\">\n          <div className=\"flex flex-col sm:flex-row gap-2\">\n            <Input\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              placeholder=\"Enter your email\"\n              className=\"flex-1 text-sm\"\n              disabled={status === 'loading'}\n            />\n            <Button\n              type=\"submit\"\n              disabled={status === 'loading' || !email.trim()}\n              variant=\"primary\"\n              size=\"sm\"\n              className=\"whitespace-nowrap\"\n              isLoading={status === 'loading'}\n            >\n              {status === 'loading' ? 'Subscribing...' : 'Subscribe'}\n            </Button>\n          </div>\n          \n          {message && (\n            <motion.div\n              initial={{ opacity: 0, y: -10 }}\n              animate={{ opacity: 1, y: 0 }}\n              className={`flex items-center gap-2 text-sm ${\n                status === 'success' ? 'text-green-600' : 'text-red-600'\n              }`}\n            >\n              {status === 'success' ? (\n                <CheckCircle className=\"w-4 h-4\" />\n              ) : (\n                <AlertCircle className=\"w-4 h-4\" />\n              )}\n              {message}\n            </motion.div>\n          )}\n        </form>\n      </div>\n    );\n  }\n\n  // Section variant\n  return (\n    <section className={`py-16 bg-gradient-to-r from-electric-blue to-indigo-600 ${className}`}>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"space-y-6\"\n        >\n          <div className=\"flex justify-center\">\n            <div className=\"bg-white/20 rounded-full p-3\">\n              <Mail className=\"w-8 h-8 text-white\" />\n            </div>\n          </div>\n          \n          <h2 className=\"text-3xl sm:text-4xl font-bold text-white\">\n            Get Mobile App Insights\n          </h2>\n          \n          <p className=\"text-xl text-blue-100 max-w-2xl mx-auto\">\n            Join 500+ entrepreneurs getting weekly tips on mobile app development, \n            industry trends, and exclusive Mobilify updates.\n          </p>\n          \n          <form onSubmit={handleSubmit} className=\"max-w-md mx-auto\">\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              <Input\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                placeholder=\"Enter your email address\"\n                className=\"flex-1 border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-electric-blue text-dark-charcoal bg-white\"\n                disabled={status === 'loading'}\n              />\n              <Button\n                type=\"submit\"\n                disabled={status === 'loading' || !email.trim()}\n                variant=\"secondary\"\n                size=\"md\"\n                className=\"bg-white text-electric-blue hover:bg-gray-50 whitespace-nowrap\"\n                isLoading={status === 'loading'}\n              >\n                {status === 'loading' ? 'Subscribing...' : 'Get Updates'}\n              </Button>\n            </div>\n            \n            {message && (\n              <motion.div\n                initial={{ opacity: 0, y: -10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className={`mt-4 flex items-center justify-center gap-2 ${\n                  status === 'success' ? 'text-green-100' : 'text-red-100'\n                }`}\n              >\n                {status === 'success' ? (\n                  <CheckCircle className=\"w-5 h-5\" />\n                ) : (\n                  <AlertCircle className=\"w-5 h-5\" />\n                )}\n                {message}\n              </motion.div>\n            )}\n          </form>\n          \n          <p className=\"text-blue-200 text-sm\">\n            No spam, ever. Unsubscribe with one click.\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default NewsletterSignup;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AALA;;;;;;AAYA,MAAM,mBAAoD,CAAC,EACzD,UAAU,QAAQ,EAClB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,UAAU;YACV,WAAW;YACX;QACF;QAEA,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;gBACV,WAAW;gBACX,SAAS;gBAET,gCAAgC;gBAChC,uCAA2D;;gBAK3D;YACF,OAAO;gBACL,UAAU;gBACV,WAAW,KAAK,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,UAAU;YACV,WAAW;QACb;QAEA,+BAA+B;QAC/B,WAAW;YACT,UAAU;YACV,WAAW;QACb,GAAG;IACL;IAEA,IAAI,YAAY,UAAU;QACxB,qBACE,8OAAC;YAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;8BACtC,8OAAC;oBAAG,WAAU;8BAA2D;;;;;;8BAGzE,8OAAC;oBAAE,WAAU;8BAA2C;;;;;;8BAIxD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qKAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;oCACV,UAAU,WAAW;;;;;;8CAEvB,8OAAC,uKAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,WAAW,aAAa,CAAC,MAAM,IAAI;oCAC7C,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,WAAW,WAAW;8CAErB,WAAW,YAAY,mBAAmB;;;;;;;;;;;;wBAI9C,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAW,CAAC,gCAAgC,EAC1C,WAAW,YAAY,mBAAmB,gBAC1C;;gCAED,WAAW,0BACV,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAExB;;;;;;;;;;;;;;;;;;;IAMb;IAEA,kBAAkB;IAClB,qBACE,8OAAC;QAAQ,WAAW,CAAC,wDAAwD,EAAE,WAAW;kBACxF,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAIpB,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAI1D,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAKvD,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qKAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,WAAU;wCACV,UAAU,WAAW;;;;;;kDAEvB,8OAAC,uKAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,WAAW,aAAa,CAAC,MAAM,IAAI;wCAC7C,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,WAAW,WAAW;kDAErB,WAAW,YAAY,mBAAmB;;;;;;;;;;;;4BAI9C,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAW,CAAC,4CAA4C,EACtD,WAAW,YAAY,mBAAmB,gBAC1C;;oCAED,WAAW,0BACV,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAExB;;;;;;;;;;;;;kCAKP,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C;uCAEe", "debugId": null}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/ChatTrigger.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle, HelpCircle, Phone } from 'lucide-react';\nimport { motion } from 'framer-motion';\nimport { crispUtils } from './CrispChat';\n\ninterface ChatTriggerProps {\n  variant?: 'button' | 'floating' | 'inline';\n  text?: string;\n  icon?: 'message' | 'help' | 'phone';\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n  context?: string; // For analytics and session data\n}\n\nconst ChatTrigger: React.FC<ChatTriggerProps> = ({\n  variant = 'button',\n  text = 'Chat with us',\n  icon = 'message',\n  className = '',\n  size = 'md',\n  context = 'general'\n}) => {\n  const handleChatOpen = () => {\n    // Set context data for better support\n    crispUtils.setSessionData({\n      trigger_context: context,\n      trigger_page: window.location.pathname,\n      trigger_time: new Date().toISOString()\n    });\n\n    // Open the chat\n    crispUtils.openChat();\n\n    // Track the interaction\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_trigger_clicked', {\n        event_category: 'engagement',\n        event_label: context,\n        custom_parameter_1: variant\n      });\n    }\n  };\n\n  const getIcon = () => {\n    const iconProps = {\n      className: size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'\n    };\n\n    switch (icon) {\n      case 'help':\n        return <HelpCircle {...iconProps} />;\n      case 'phone':\n        return <Phone {...iconProps} />;\n      default:\n        return <MessageCircle {...iconProps} />;\n    }\n  };\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'px-3 py-2 text-sm';\n      case 'lg':\n        return 'px-6 py-4 text-lg';\n      default:\n        return 'px-4 py-3 text-base';\n    }\n  };\n\n  if (variant === 'floating') {\n    return (\n      <motion.button\n        onClick={handleChatOpen}\n        className={`fixed bottom-6 right-6 bg-electric-blue text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-40 ${className}`}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        initial={{ opacity: 0, scale: 0 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ delay: 2, duration: 0.3 }}\n        title={text}\n      >\n        {getIcon()}\n        <span className=\"sr-only\">{text}</span>\n      </motion.button>\n    );\n  }\n\n  if (variant === 'inline') {\n    return (\n      <button\n        onClick={handleChatOpen}\n        className={`inline-flex items-center gap-2 text-electric-blue hover:text-indigo-700 font-medium transition-colors duration-200 ${className}`}\n      >\n        {getIcon()}\n        <span>{text}</span>\n      </button>\n    );\n  }\n\n  // Default button variant\n  return (\n    <motion.button\n      onClick={handleChatOpen}\n      className={`inline-flex items-center gap-2 bg-electric-blue text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md hover:shadow-lg ${getSizeClasses()} ${className}`}\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {getIcon()}\n      <span>{text}</span>\n    </motion.button>\n  );\n};\n\nexport default ChatTrigger;\n\n// Pre-configured chat triggers for common use cases\nexport const ChatTriggers = {\n  // For the header/navigation\n  HeaderChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Live Chat\"\n      icon=\"message\"\n      context=\"header\"\n      size=\"sm\"\n    />\n  ),\n\n  // For the contact section\n  ContactChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Chat with Our Team\"\n      icon=\"message\"\n      context=\"contact\"\n      size=\"lg\"\n      className=\"w-full sm:w-auto\"\n    />\n  ),\n\n  // For the services page\n  ServicesChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Ask About Pricing\"\n      icon=\"help\"\n      context=\"services\"\n      size=\"md\"\n    />\n  ),\n\n  // For the FAQ page\n  FAQChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Still have questions? Chat with us\"\n      icon=\"help\"\n      context=\"faq\"\n      size=\"md\"\n    />\n  ),\n\n  // For blog posts\n  BlogChat: () => (\n    <ChatTrigger\n      variant=\"inline\"\n      text=\"Discuss this article\"\n      icon=\"message\"\n      context=\"blog\"\n      size=\"sm\"\n    />\n  ),\n\n  // Floating action button (always visible)\n  FloatingChat: () => (\n    <ChatTrigger\n      variant=\"floating\"\n      text=\"Chat with us\"\n      icon=\"message\"\n      context=\"floating\"\n    />\n  ),\n\n  // For the demo section\n  DemoChat: () => (\n    <ChatTrigger\n      variant=\"button\"\n      text=\"Get Help with Demo\"\n      icon=\"help\"\n      context=\"demo\"\n      size=\"md\"\n    />\n  )\n};\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AACA;AACA;AALA;;;;;AAgBA,MAAM,cAA0C,CAAC,EAC/C,UAAU,QAAQ,EAClB,OAAO,cAAc,EACrB,OAAO,SAAS,EAChB,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACpB;IACC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,+HAAA,CAAA,aAAU,CAAC,cAAc,CAAC;YACxB,iBAAiB;YACjB,cAAc,OAAO,QAAQ,CAAC,QAAQ;YACtC,cAAc,IAAI,OAAO,WAAW;QACtC;QAEA,gBAAgB;QAChB,+HAAA,CAAA,aAAU,CAAC,QAAQ;QAEnB,wBAAwB;QACxB,uCAA2D;;QAM3D;IACF;IAEA,MAAM,UAAU;QACd,MAAM,YAAY;YAChB,WAAW,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY;QACrE;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,8NAAA,CAAA,aAAU;oBAAE,GAAG,SAAS;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAE,GAAG,SAAS;;;;;;YAC7B;gBACE,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAE,GAAG,SAAS;;;;;;QACvC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,YAAY,YAAY;QAC1B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;YACZ,SAAS;YACT,WAAW,CAAC,+IAA+I,EAAE,WAAW;YACxK,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,OAAO;gBAAG,UAAU;YAAI;YACtC,OAAO;;gBAEN;8BACD,8OAAC;oBAAK,WAAU;8BAAW;;;;;;;;;;;;IAGjC;IAEA,IAAI,YAAY,UAAU;QACxB,qBACE,8OAAC;YACC,SAAS;YACT,WAAW,CAAC,mHAAmH,EAAE,WAAW;;gBAE3I;8BACD,8OAAC;8BAAM;;;;;;;;;;;;IAGb;IAEA,yBAAyB;IACzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAW,CAAC,2JAA2J,EAAE,iBAAiB,CAAC,EAAE,WAAW;QACxM,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;;YAEvB;0BACD,8OAAC;0BAAM;;;;;;;;;;;;AAGb;uCAEe;AAGR,MAAM,eAAe;IAC1B,4BAA4B;IAC5B,YAAY,kBACV,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,0BAA0B;IAC1B,aAAa,kBACX,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;YACL,WAAU;;;;;;IAId,wBAAwB;IACxB,cAAc,kBACZ,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,mBAAmB;IACnB,SAAS,kBACP,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,iBAAiB;IACjB,UAAU,kBACR,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;IAIT,0CAA0C;IAC1C,cAAc,kBACZ,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;;;;;;IAIZ,uBAAuB;IACvB,UAAU,kBACR,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,MAAK;YACL,SAAQ;YACR,MAAK;;;;;;AAGX", "debugId": null}}, {"offset": {"line": 2757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ChatTriggers } from './ChatTrigger';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // Get Web3Forms access key from environment variables\n      // Set WEB3FORMS_ACCESS_KEY in your .env.local file\n      const accessKey = process.env.NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY || 'YOUR_WEB3FORMS_ACCESS_KEY';\n\n      const response = await fetch('https://api.web3forms.com/submit', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          access_key: accessKey,\n          name: formData.name,\n          email: formData.email,\n          message: formData.message,\n          from_name: 'Mobilify Contact Form',\n          subject: 'New Contact Form Submission from Mobilify Website'\n        }),\n      });\n\n      if (response.ok) {\n        setSubmitStatus('success');\n        setFormData({ name: '', email: '', message: '' });\n        \n        // Track form submission event (placeholder for GA4)\n        if (typeof window !== 'undefined' && (window as any).gtag) {\n          (window as any).gtag('event', 'form_submission', {\n            event_category: 'engagement',\n            event_label: 'contact_form'\n          });\n        }\n      } else {\n        setSubmitStatus('error');\n      }\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n            Ready to Build Your Mobile Future?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote.\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"max-w-2xl mx-auto\"\n        >\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Name *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200\"\n                  placeholder=\"Your full name\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email *\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Briefly describe your project *\n              </label>\n              <textarea\n                id=\"message\"\n                name=\"message\"\n                value={formData.message}\n                onChange={handleChange}\n                required\n                rows={6}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200 resize-vertical\"\n                placeholder=\"Tell us about your app idea, website you'd like to convert, or any specific requirements...\"\n              />\n            </div>\n            \n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"w-full bg-electric-blue text-white py-4 px-6 rounded-lg font-semibold hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\"\n            >\n              {isSubmitting ? 'Sending...' : 'Send Message'}\n            </button>\n          </form>\n\n          {/* Alternative Contact Method */}\n          <div className=\"mt-8 text-center\">\n            <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n              Prefer to chat? Get instant answers to your questions.\n            </p>\n            <ChatTriggers.ContactChat />\n          </div>\n\n          {/* Status Messages */}\n          {submitStatus === 'success' && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg\"\n            >\n              <p className=\"text-green-800 text-center\">\n                Thank you! We've received your message and will get back to you within 24 hours.\n              </p>\n            </motion.div>\n          )}\n          \n          {submitStatus === 'error' && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg\"\n            >\n              <p className=\"text-red-800 text-center\">\n                An error occurred. Please try again or email us <NAME_EMAIL>.\n              </p>\n            </motion.div>\n          )}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,sDAAsD;YACtD,mDAAmD;YACnD,MAAM,YAAY,wCAAgD;YAElE,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY;oBACZ,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;oBACzB,WAAW;oBACX,SAAS;gBACX;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB;gBAChB,YAAY;oBAAE,MAAM;oBAAI,OAAO;oBAAI,SAAS;gBAAG;gBAE/C,oDAAoD;gBACpD,uCAA2D;;gBAK3D;YACF,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA+C;;;;;;8DAG/E,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,QAAQ;4CACR,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,eAAe,eAAe;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,8OAAC,iIAAA,CAAA,eAAY,CAAC,WAAW;;;;;;;;;;;wBAI1B,iBAAiB,2BAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;wBAM7C,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;uCAEe", "debugId": null}}, {"offset": {"line": 3103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My%20projects/Mobilify/website/gemini/mobilify-website/src/components/SimpleFloatingChat.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { MessageCircle } from 'lucide-react';\n\nconst SimpleFloatingChat: React.FC = () => {\n  const handleChatOpen = () => {\n    // Try to open Crisp chat if available\n    if (typeof window !== 'undefined' && (window as any).$crisp) {\n      (window as any).$crisp.push(['do', 'chat:open']);\n    } else {\n      // Fallback to mailto if Crisp is not available\n      window.location.href = 'mailto:<EMAIL>?subject=Chat%20Request';\n    }\n\n    // Track chat interaction for analytics\n    if (typeof window !== 'undefined' && (window as any).gtag) {\n      (window as any).gtag('event', 'chat_opened', {\n        event_category: 'engagement',\n        event_label: 'floating_chat'\n      });\n    }\n  };\n\n  return (\n    <button\n      onClick={handleChatOpen}\n      className=\"fixed bottom-6 right-6 z-50 bg-electric-blue text-white p-4 rounded-full shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200 group\"\n      aria-label=\"Open chat\"\n    >\n      <MessageCircle className=\"w-6 h-6\" />\n      \n      {/* Tooltip */}\n      <div className=\"absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\">\n        Chat with us\n        <div className=\"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900\"></div>\n      </div>\n    </button>\n  );\n};\n\nexport default SimpleFloatingChat;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,qBAA+B;IACnC,MAAM,iBAAiB;QACrB,sCAAsC;QACtC,uCAA6D;;QAE7D,OAAO;YACL,+CAA+C;YAC/C,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,uCAAuC;QACvC,uCAA2D;;QAK3D;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;;0BAEX,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BAGzB,8OAAC;gBAAI,WAAU;;oBAA4K;kCAEzL,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}]}